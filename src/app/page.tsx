"use client"

import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { FlipCard } from "@/components/flip-card"
import { DiamondSection } from "@/components/diamond-section"
import { InteractiveImageSection } from "@/components/interactive-image-section"
import { ProductOverviewSection } from "@/components/product-overview-section"
import { ProblemSolutionSection } from "@/components/problem-solution-section"
import { HowItWorksSection } from "@/components/how-it-works-section"
import { ThreePillarsSection } from "@/components/three-pillars-section"
import { WaitlistCTASection } from "@/components/waitlist-cta-section"
import { ScrollIndicator } from "@/components/ScrollIndicator"
import { getSectionContent, HeroContent, TeamContent, VideoHeroContent } from "@/lib/content"
import { heroContainer, heroText, heroButton, getAnimationVariants } from "@/lib/animations"
import { useSafeScrollTransition } from "@/lib/hooks/useSafeScrollTransition"

export default function VideoLandingPage() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const videoRefs = useRef<(HTMLVideoElement | null)[]>([])

  // Get content from externalized sources
  const heroContent = getSectionContent(1) as HeroContent
  const teamContent = getSectionContent(9) as TeamContent
  const videoHeroContent = getSectionContent(10) as VideoHeroContent
  const videoData = videoHeroContent.videos.slice(0, 2) // Only show first 2 videos
  const teamData = teamContent.members

  // Safe scroll transition system
  const {
    heroTransforms,
    section2Transforms,
    resetTransition,
    hasTriggered
  } = useSafeScrollTransition({
    threshold: 50
  })

  // Development helper - press 'r' to reset transition
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === 'r' && (event.metaKey || event.ctrlKey)) {
        event.preventDefault()
        resetTransition()
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [resetTransition])

  // Auto-advance slides
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % videoData.length)
    }, 5000) // Change slide every 5 seconds

    return () => clearInterval(interval)
  }, [isAutoPlaying, videoData.length])

  // Control video playback when slide changes
  useEffect(() => {
    videoRefs.current.forEach((video, index) => {
      if (video) {
        if (index === currentSlide && videoData[index].isVideo) {
          // Play the current video
          video.currentTime = 0 // Restart from beginning
          video.play().catch((error) => {
            console.log("Video play failed:", error)
          })
        } else {
          // Pause all other videos
          video.pause()
        }
      }
    })
  }, [currentSlide, videoData])

  const handleDotClick = (index: number) => {
    setCurrentSlide(index)
    setIsAutoPlaying(false)
    // Resume auto-play after 10 seconds
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  const currentVideo = videoData[currentSlide]

  return (
    <div className="relative w-full">
      {/* Video Hero Section - Full Screen */}
      <motion.section
        className="relative h-screen w-full overflow-hidden"
        style={{
          backgroundColor: 'var(--color-black)',
          y: heroTransforms.y,
          opacity: heroTransforms.opacity,
          scale: heroTransforms.scale
        }}
      >
        {/* Background Videos */}
        <div className="absolute inset-0">
          {videoData.map((video, index) => (
            <div
              key={video.id}
              className={`absolute inset-0 transition-opacity ${
                index === currentSlide ? "opacity-100" : "opacity-0"
              }`}
              style={{ transitionDuration: 'var(--duration-1000)' }}
            >
              {/* Render actual video element or background image */}
              {video.isVideo ? (
                <video
                  ref={(el) => {
                    videoRefs.current[index] = el
                  }}
                  className="h-full w-full object-cover"
                  muted
                  loop
                  playsInline
                  preload="metadata"
                >
                  <source src={video.videoUrl} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              ) : (
                <div
                  className="h-full w-full bg-cover bg-center bg-no-repeat"
                  style={{
                    backgroundImage: `url('${video.videoUrl}')`,
                  }}
                />
              )}
              {/* Video overlay for better text readability */}
              <div className="absolute inset-0 video-overlay" />
            </div>
          ))}
        </div>

        {/* Content Overlay */}
        <div className="relative z-10 flex h-full items-center justify-between px-8 md:px-16 lg:px-24">
          {/* Left Content */}
          <motion.div
            className="max-w-2xl"
            style={{ color: 'var(--color-white)' }}
            variants={getAnimationVariants(heroContainer)}
            initial="hidden"
            animate="visible"
          >
            <motion.h1
              className="mb-4 text-4xl font-bold leading-tight md:text-5xl lg:text-6xl"
              variants={getAnimationVariants(heroText)}
            >
              {heroContent.headline}
            </motion.h1>
            <motion.p
              className="mb-8 text-lg leading-relaxed md:text-xl lg:text-2xl"
              style={{ color: 'var(--color-gray-200)' }}
              variants={getAnimationVariants(heroText)}
            >
              {heroContent.subheadline}
            </motion.p>
            <motion.div variants={getAnimationVariants(heroButton)}>
              <Button size="lg" className="btn-cta">
                {heroContent.ctaButton}
              </Button>
            </motion.div>
            {heroContent.additionalText && (
              <motion.p
                className="mt-4 text-sm"
                style={{ color: 'var(--color-gray-300)' }}
                variants={getAnimationVariants(heroText)}
              >
                {heroContent.additionalText}
              </motion.p>
            )}
          </motion.div>

          {/* Right Content - Statistics Display */}
          <motion.div
            className="hidden md:block"
            style={{ color: 'var(--color-white)' }}
            variants={getAnimationVariants(heroContainer)}
            initial="hidden"
            animate="visible"
          >
            <motion.div
              className="stat-display"
              variants={getAnimationVariants(heroText)}
            >
              <motion.div
                className="stat-number"
                variants={getAnimationVariants(heroText)}
              >
                {currentVideo.statistic}
              </motion.div>
              <motion.div
                className="stat-type"
                variants={getAnimationVariants(heroText)}
              >
                {currentVideo.statType}
              </motion.div>
              <motion.div
                className="stat-metric"
                variants={getAnimationVariants(heroText)}
              >
                {currentVideo.statMetric}
              </motion.div>
            </motion.div>
          </motion.div>
        </div>

        {/* Navigation Dots - Bottom Right */}
        <div className="absolute bottom-8 right-8 z-20 flex space-x-3">
          {videoData.map((_, index) => (
            <button
              key={index}
              onClick={() => handleDotClick(index)}
              className={`nav-dot ${
                index === currentSlide ? "active" : "inactive"
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>

        {/* Progress Bar */}
        <div className="progress-bar-container">
          <div className="progress-bar-track">
            <div
              className="progress-bar-fill"
              style={{
                width: `${((currentSlide + 1) / videoData.length) * 100}%`,
              }}
            />
          </div>
        </div>

        {/* Mobile Statistics Display */}
        <div className="absolute top-8 right-8 z-20 md:hidden text-right" style={{ color: 'var(--color-white)' }}>
          <div className="text-4xl font-light opacity-90">{currentVideo.statistic}</div>
          <div className="text-sm font-medium opacity-80">{currentVideo.statType}</div>
          <div className="text-xs opacity-70">{currentVideo.statMetric}</div>
        </div>

        {/* Scroll Indicator */}
        <ScrollIndicator isVisible={!hasTriggered} />
      </motion.section>

      {/* Section 2: Product Overview - Presentation Style */}
      <motion.div
        className="relative"
        style={{
          y: section2Transforms.y,
          opacity: section2Transforms.opacity
        }}
      >
        <ProductOverviewSection />
      </motion.div>

      {/* Section 3: Problem, Solution, Benefits */}
      <ProblemSolutionSection />

      {/* Section 4: How It Works */}
      <HowItWorksSection />

      {/* Section 5: Three Pillars of Health */}
      <ThreePillarsSection />

      {/* Section 6: The Science - Diamond Section */}
      <DiamondSection />

      {/* Section 7: Product Features - Interactive Image Section */}
      <InteractiveImageSection />

      {/* Our Team Section */}
      <section className="py-16 px-8 md:px-16 lg:px-24 min-h-screen flex items-center" style={{ backgroundColor: 'var(--color-gray-50)' }}>
        <div className="max-w-6xl mx-auto w-full">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12" style={{ color: 'var(--color-gray-900)' }}>{teamContent.title}</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
            {teamData.map((partner) => (
              <FlipCard key={partner.id} partner={partner} />
            ))}
          </div>
        </div>
      </section>

      {/* Section 8: Waitlist CTA */}
      <WaitlistCTASection />
    </div>
  )
}
