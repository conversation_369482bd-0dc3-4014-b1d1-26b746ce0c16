"use client"

import { getSectionContent, ThreePillarsContent } from "@/lib/content"

export function ThreePillarsSection() {
  const content = getSectionContent(5) as ThreePillarsContent

  // Predefined chart bar heights to avoid hydration mismatch
  const chartBarHeights = [
    [45, 72, 38, 65, 52], // Cardiovascular Health
    [58, 41, 76, 33, 69], // Metabolic Health
    [62, 48, 81, 29, 55]  // Cognitive Performance
  ]

  return (
    <section className="py-16 px-8 md:px-16 lg:px-24" style={{ backgroundColor: 'var(--color-gray-50)' }}>
      <div className="max-w-6xl mx-auto w-full">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-16" style={{ color: 'var(--color-gray-900)' }}>
          {content.title}
        </h2>
        
        <div className="space-y-16 md:space-y-24">
          {content.pillars.map((pillar, index) => (
            <div key={index} className={`flex flex-col ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'} items-center gap-8 md:gap-16`}>
              {/* Content */}
              <div className="flex-1 text-center md:text-left">
                <h3 className="text-2xl md:text-3xl font-bold mb-6" style={{ color: 'var(--color-gray-900)' }}>
                  {pillar.title}
                </h3>
                <p className="text-lg leading-relaxed mb-6" style={{ color: 'var(--color-gray-600)' }}>
                  {pillar.description}
                </p>
                <div className="pillar-metrics">
                  <div className="metric-item">
                    <div className="metric-icon">
                      {index === 0 && (
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      )}
                      {index === 1 && (
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      )}
                      {index === 2 && (
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      )}
                    </div>
                    <span className="metric-text">Enhanced Performance</span>
                  </div>
                </div>
              </div>

              {/* Device Mockup */}
              <div className="flex-1">
                <div className="device-mockup-container">
                  <div className="device-mockup">
                    <div className="mockup-screen">
                      <div className="mockup-content">
                        <div className="mockup-header">
                          <div className="mockup-title">
                            {pillar.title.split(' ')[0]} Monitor
                          </div>
                        </div>
                        <div className="mockup-chart">
                          <div className="chart-bars">
                            {chartBarHeights[index].map((height, i) => (
                              <div
                                key={i}
                                className="chart-bar"
                                style={{
                                  height: `${height}%`,
                                  animationDelay: `${i * 0.1}s`
                                }}
                              />
                            ))}
                          </div>
                        </div>
                        <div className="mockup-stats">
                          <div className="stat-item">
                            <div className="stat-value">
                              {index === 0 ? '85 BPM' : index === 1 ? '95 mg/dL' : '98%'}
                            </div>
                            <div className="stat-label">
                              {index === 0 ? 'Heart Rate' : index === 1 ? 'Glucose' : 'Focus'}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <p className="text-sm mt-4 text-center" style={{ color: 'var(--color-gray-500)' }}>
                    {pillar.mockup}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <style jsx>{`
        .pillar-metrics {
          display: flex;
          justify-content: center;
        }
        
        @media (min-width: 768px) {
          .pillar-metrics {
            justify-content: flex-start;
          }
        }
        
        .metric-item {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 0.75rem 1.5rem;
          background: var(--color-white);
          border-radius: 2rem;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .metric-icon {
          color: var(--color-brand-blue-600);
        }
        
        .metric-text {
          font-weight: 600;
          color: var(--color-gray-700);
        }
        
        .device-mockup-container {
          display: flex;
          flex-direction: column;
          align-items: center;
        }
        
        .device-mockup {
          width: 280px;
          height: 400px;
          background: linear-gradient(145deg, #1f2937, #374151);
          border-radius: 2rem;
          padding: 1.5rem;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
          position: relative;
        }
        
        .mockup-screen {
          width: 100%;
          height: 100%;
          background: var(--color-white);
          border-radius: 1.5rem;
          padding: 1.5rem;
          overflow: hidden;
        }
        
        .mockup-content {
          height: 100%;
          display: flex;
          flex-direction: column;
        }
        
        .mockup-header {
          margin-bottom: 1.5rem;
        }
        
        .mockup-title {
          font-size: 1.125rem;
          font-weight: bold;
          color: var(--color-gray-900);
          text-align: center;
        }
        
        .mockup-chart {
          flex: 1;
          display: flex;
          align-items: end;
          justify-content: center;
          margin-bottom: 1.5rem;
        }
        
        .chart-bars {
          display: flex;
          align-items: end;
          gap: 0.5rem;
          height: 120px;
        }
        
        .chart-bar {
          width: 20px;
          background: linear-gradient(to top, var(--color-brand-blue-600), var(--color-brand-blue-400));
          border-radius: 0.25rem;
          animation: chartGrow 1s ease-out forwards;
          opacity: 0;
        }
        
        @keyframes chartGrow {
          to {
            opacity: 1;
          }
        }
        
        .mockup-stats {
          text-align: center;
        }
        
        .stat-item {
          margin-bottom: 0.5rem;
        }
        
        .stat-value {
          font-size: 1.5rem;
          font-weight: bold;
          color: var(--color-brand-blue-600);
        }
        
        .stat-label {
          font-size: 0.875rem;
          color: var(--color-gray-600);
        }
        
        @media (max-width: 768px) {
          .device-mockup {
            width: 240px;
            height: 340px;
            padding: 1.25rem;
          }
          
          .chart-bars {
            height: 80px;
          }
        }
      `}</style>
    </section>
  )
}
