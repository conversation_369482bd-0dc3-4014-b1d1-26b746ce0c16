"use client"

import { motion } from "framer-motion"
import { getSectionContent, ProductOverviewContent } from "@/lib/content"
import { useInViewAnimation } from "@/lib/hooks/useInViewAnimation"
import { fadeInUp, staggerContainer, getAnimationVariants } from "@/lib/animations"

export function ProductOverviewSection() {
  const content = getSectionContent(2) as ProductOverviewContent
  const { ref, isInView } = useInViewAnimation({ threshold: 0.2 })

  return (
    <section
      id="product-overview"
      className="py-16 px-8 md:px-16 lg:px-24 min-h-screen flex items-center relative"
      style={{
        backgroundImage: 'url(/images/product-overview-background.jpg)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* Background overlay for better text readability */}
      <div className="absolute inset-0 bg-black/70"></div>
      <motion.div
        ref={ref}
        className="max-w-6xl mx-auto w-full relative z-10"
        variants={getAnimationVariants(staggerContainer)}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
      >
        <motion.h2
          className="text-4xl md:text-5xl font-bold text-center mb-16 text-white"
          variants={getAnimationVariants(fadeInUp)}
        >
          {content.title}
        </motion.h2>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12"
          variants={getAnimationVariants(staggerContainer)}
        >
          {content.features.map((feature, index) => (
            <motion.div
              key={index}
              className="text-center"
              variants={getAnimationVariants(fadeInUp)}
            >
              {/* Corner bracket design */}
              <div className="relative mb-6">
                <div className="feature-card-container">
                  <div className="feature-card">
                    {/* Top-left corner */}
                    <div className="corner-bracket corner-top-left"></div>
                    {/* Top-right corner */}
                    <div className="corner-bracket corner-top-right"></div>
                    {/* Bottom-left corner */}
                    <div className="corner-bracket corner-bottom-left"></div>
                    {/* Bottom-right corner */}
                    <div className="corner-bracket corner-bottom-right"></div>
                    
                    <div className="feature-card-content">
                      <h3 className="text-2xl md:text-3xl font-bold mb-2" style={{ color: 'var(--color-gray-900)' }}>
                        {feature.title}
                      </h3>
                      <p className="text-lg" style={{ color: 'var(--color-gray-600)' }}>
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>
      
      <style jsx>{`
        .feature-card-container {
          position: relative;
          display: inline-block;
          width: 100%;
        }
        
        .feature-card {
          position: relative;
          padding: 3rem 2rem;
          border: 2px solid var(--color-gray-200);
          border-radius: 0.5rem;
          background: var(--color-white);
          transition: all 0.3s ease;
          min-height: 200px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .feature-card:hover {
          border-color: var(--color-brand-blue-600);
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);
        }
        
        .feature-card-content {
          text-align: center;
          z-index: 2;
          position: relative;
        }
        
        .corner-bracket {
          position: absolute;
          width: 20px;
          height: 20px;
          border: 3px solid var(--color-brand-blue-600);
          transition: all 0.3s ease;
        }
        
        .corner-top-left {
          top: -2px;
          left: -2px;
          border-right: none;
          border-bottom: none;
        }
        
        .corner-top-right {
          top: -2px;
          right: -2px;
          border-left: none;
          border-bottom: none;
        }
        
        .corner-bottom-left {
          bottom: -2px;
          left: -2px;
          border-right: none;
          border-top: none;
        }
        
        .corner-bottom-right {
          bottom: -2px;
          right: -2px;
          border-left: none;
          border-top: none;
        }
        
        .feature-card:hover .corner-bracket {
          border-color: var(--color-brand-blue-700);
          width: 25px;
          height: 25px;
        }
        
        @media (max-width: 768px) {
          .feature-card {
            padding: 2rem 1.5rem;
            min-height: 160px;
          }
          
          .corner-bracket {
            width: 15px;
            height: 15px;
          }
          
          .feature-card:hover .corner-bracket {
            width: 18px;
            height: 18px;
          }
        }
      `}</style>
    </section>
  )
}
