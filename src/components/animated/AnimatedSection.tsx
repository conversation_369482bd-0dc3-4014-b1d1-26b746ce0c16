"use client"

import { motion } from "framer-motion"
import { ReactNode } from "react"
import { useInViewAnimation } from "@/lib/hooks/useInViewAnimation"
import { getAnimationVariants, staggerContainer } from "@/lib/animations"

interface AnimatedSectionProps {
  children: ReactNode
  className?: string
  threshold?: number
  stagger?: boolean
}

export function AnimatedSection({ 
  children, 
  className = "", 
  threshold = 0.3,
  stagger = false 
}: AnimatedSectionProps) {
  const { ref, isInView } = useInViewAnimation({ threshold })

  return (
    <motion.div
      ref={ref}
      className={className}
      variants={getAnimationVariants(stagger ? staggerContainer : { 
        hidden: { opacity: 0, y: 30 },
        visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } }
      })}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
    >
      {children}
    </motion.div>
  )
}
