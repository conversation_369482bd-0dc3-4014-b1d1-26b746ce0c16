"use client"

import { motion } from "framer-motion"
import { ReactNode } from "react"
import { useInViewAnimation } from "@/lib/hooks/useInViewAnimation"
import { getAnimationVariants, fadeInUp, fadeInLeft, fadeInRight } from "@/lib/animations"

interface AnimatedTextProps {
  children: ReactNode
  className?: string
  direction?: 'up' | 'left' | 'right'
  delay?: number
  threshold?: number
}

export function AnimatedText({
  children,
  className = "",
  direction = 'up',
  delay = 0,
  threshold = 0.3
}: AnimatedTextProps) {
  const { ref, isInView } = useInViewAnimation({ threshold })

  const getVariant = () => {
    switch (direction) {
      case 'left': return fadeInLeft
      case 'right': return fadeInRight
      default: return fadeInUp
    }
  }

  return (
    <motion.div
      ref={ref}
      className={className}
      variants={getAnimationVariants(getVariant())}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      transition={{ delay }}
    >
      {children}
    </motion.div>
  )
}
