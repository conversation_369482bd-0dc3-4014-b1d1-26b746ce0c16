"use client"

import { motion } from "framer-motion"
import { useEffect, useState } from "react"

interface ScrollIndicatorProps {
  isVisible?: boolean
  className?: string
}

export function ScrollIndicator({ isVisible = true, className = "" }: ScrollIndicatorProps) {
  const [shouldShow, setShouldShow] = useState(false)

  useEffect(() => {
    // Show indicator after a brief delay to let hero animations complete
    const timer = setTimeout(() => setShouldShow(true), 2000)
    return () => clearTimeout(timer)
  }, [])

  if (!isVisible || !shouldShow) return null

  return (
    <motion.div
      data-scroll-indicator
      className={`fixed bottom-8 left-1/2 transform -translate-x-1/2 z-30 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <motion.div
        className="flex flex-col items-center text-white/80"
        animate={{ y: [0, -8, 0] }}
        transition={{ 
          duration: 2, 
          repeat: Infinity, 
          ease: "easeInOut" 
        }}
      >
        {/* Scroll text */}
        <div className="text-sm font-medium mb-2 tracking-wide">
          SCROLL TO CONTINUE
        </div>
        
        {/* Animated arrow */}
        <motion.div
          className="w-6 h-10 border-2 border-white/60 rounded-full flex justify-center"
          whileHover={{ borderColor: "rgba(255, 255, 255, 0.9)" }}
        >
          <motion.div
            className="w-1 h-3 bg-white/60 rounded-full mt-2"
            animate={{ y: [0, 12, 0], opacity: [1, 0, 1] }}
            transition={{ 
              duration: 1.5, 
              repeat: Infinity, 
              ease: "easeInOut" 
            }}
          />
        </motion.div>
      </motion.div>
    </motion.div>
  )
}
