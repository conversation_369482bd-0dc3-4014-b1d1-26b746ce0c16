"use client"

import { useState } from "react"
import { X } from "lucide-react"
import Image from "next/image"
import { getSectionContent, ProductFeaturesContent } from "@/lib/content"

interface HotspotData {
  id: number
  x: number // percentage from left
  y: number // percentage from top
  title: string
  description: string
  details: string
}

export function InteractiveImageSection() {
  const content = getSectionContent(7) as ProductFeaturesContent
  const hotspots: HotspotData[] = content.hotspots
  const [activeHotspot, setActiveHotspot] = useState<HotspotData | null>(null)

  const closeModal = () => {
    setActiveHotspot(null)
  }

  return (
    <section className="relative h-screen w-full overflow-hidden" style={{ backgroundColor: 'var(--color-white)' }}>
      {/* Section Header */}
      <div className="absolute top-8 left-0 right-0 z-10 text-center">
        <h2 className="text-4xl md:text-5xl font-bold mb-4" style={{ color: 'var(--color-gray-900)' }}>{content.title}</h2>
        <p className="text-lg max-w-2xl mx-auto px-4" style={{ color: 'var(--color-gray-600)' }}>
          {content.subtitle}
        </p>
      </div>

      {/* Large Background Image */}
      <div className="relative w-full h-full flex items-center justify-center pt-24 pb-8">
        <div className="relative max-w-4xl w-full h-full flex items-center justify-center">
          <Image
            src="/images/vitaliti-air-no-background.png"
            alt={content.deviceAltText}
            width={800}
            height={600}
            className="max-w-full max-h-full object-contain"
            priority
          />

          {/* Interactive Hotspots */}
          {hotspots.map((hotspot) => (
            <button
              key={hotspot.id}
              className="absolute transform -translate-x-1/2 -translate-y-1/2 group"
              style={{
                left: `${hotspot.x}%`,
                top: `${hotspot.y}%`,
              }}
              onClick={() => setActiveHotspot(hotspot)}
            >
              {/* Pulsating Dot */}
              <div className="relative">
                {/* Outer pulsating ring */}
                <div className="hotspot-ring-outer"></div>
                {/* Middle ring */}
                <div className="hotspot-ring-middle"></div>
                {/* Inner dot */}
                <div className="hotspot-dot">
                  <div className="hotspot-dot-inner"></div>
                </div>
              </div>

              {/* Tooltip on hover */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                <div className="hotspot-tooltip">
                  <div className="font-semibold">{hotspot.title}</div>
                  <div className="text-xs" style={{ color: 'var(--color-gray-300)' }}>{hotspot.description}</div>
                  {/* Arrow pointing down */}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent" style={{ borderTopColor: 'rgba(17, 24, 39, 0.9)' }}></div>
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Modal Popup */}
      {activeHotspot && (
        <div className="modal-overlay">
          <div className="modal-content">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b" style={{ borderColor: 'var(--color-gray-200)' }}>
              <div>
                <h3 className="text-2xl font-bold" style={{ color: 'var(--color-gray-900)' }}>{activeHotspot.title}</h3>
                <p className="mt-1 font-medium" style={{ color: 'var(--color-brand-blue-600)' }}>{activeHotspot.description}</p>
              </div>
              <button onClick={closeModal} className="p-2 rounded-full transition-colors" style={{ backgroundColor: 'transparent' }} onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--color-gray-100)'} onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}>
                <X className="w-5 h-5" style={{ color: 'var(--color-gray-500)' }} />
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6">
              <p className="leading-relaxed" style={{ color: 'var(--color-gray-700)' }}>{activeHotspot.details}</p>

              {/* Action Buttons */}
              <div className="flex gap-3 mt-6">
                <button className="flex-1 px-4 py-2 rounded-lg transition-colors font-medium" style={{ backgroundColor: 'var(--color-brand-blue-600)', color: 'var(--color-white)' }} onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--color-brand-blue-700)'} onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--color-brand-blue-600)'}>
                  Learn More
                </button>
                <button
                  onClick={closeModal}
                  className="flex-1 px-4 py-2 rounded-lg transition-colors font-medium" style={{ backgroundColor: 'var(--color-gray-100)', color: 'var(--color-gray-700)' }} onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--color-gray-200)'} onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--color-gray-100)'}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  )
} 