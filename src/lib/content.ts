// Content utility for parsing home page markdown content

export interface HomeSection {
  id: number
  title: string
  content: Record<string, unknown>
}

export interface HeroContent {
  headline: string
  subheadline: string
  ctaButton: string
  additionalText?: string
}

export interface ProductOverviewContent {
  title: string
  features: Array<{
    title: string
    description: string
  }>
}

export interface ProblemSolutionContent {
  title: string
  problem: string
  solution: string
  benefits: string
  labels: {
    problemLabel: string
    solutionLabel: string
    benefitsLabel: string
  }
}

export interface HowItWorksContent {
  title: string
  steps: Array<{
    number: number
    title: string
    image: string
    text: string
  }>
}

export interface ThreePillarsContent {
  title: string
  layout: string
  pillars: Array<{
    title: string
    mockup: string
    description: string
  }>
}

export interface ScienceContent {
  title: string
  inactiveTitle: string
  inactiveDescription: string
  statistic: string
  citations: string
  socialCredibility: string
  visual: string
  segments: Array<{
    number: number
    title: string
    description: string
    position: string
  }>
}

export interface ProductFeaturesContent {
  title: string
  subtitle: string
  deviceAltText: string
  hotspots: Array<{
    id: number
    x: number
    y: number
    title: string
    description: string
    details: string
  }>
}

export interface WaitlistCTAContent {
  background: string
  headline: string
  subheadline: string
  emailPlaceholder: string
  button: string
  text: string
  successTitle: string
  successMessage: string
}

export interface TeamContent {
  title: string
  members: Array<{
    id: number
    name: string
    title: string
    photo: string
  }>
}

export interface VideoHeroContent {
  videos: Array<{
    id: number
    title: string
    subtitle: string
    videoUrl: string
    isVideo: boolean
    number: string
    statistic: string
    statType: string
    statMetric: string
  }>
}

// Parse the markdown content into structured data
export function parseHomeContent(): {
  hero: HeroContent
  productOverview: ProductOverviewContent
  problemSolution: ProblemSolutionContent
  howItWorks: HowItWorksContent
  threePillars: ThreePillarsContent
  science: ScienceContent
  productFeatures: ProductFeaturesContent
  waitlistCTA: WaitlistCTAContent
  team: TeamContent
  videoHero: VideoHeroContent
} {
  // For now, return structured content based on the markdown
  // In a real implementation, you could parse the actual markdown file
  return {
    hero: {
      headline: "Revitalizing Human Energy. One cell at a time.",
      subheadline: "Just 30 minutes a day, 3 times a week. Scientifically proven to increase VO2 max and extend healthspan.",
      ctaButton: "JOIN THE WAITLIST",
      additionalText: "What happens to the cells as we age?"
    },
    productOverview: {
      title: "Vitaliti Air at a glance",
      features: [
        {
          title: "30 min sessions",
          description: "Simple time commitment"
        },
        {
          title: "3x per week", 
          description: "Minimal lifestyle disruption"
        },
        {
          title: "Proven results",
          description: "Increased VO2 max & longevity"
        }
      ]
    },
    problemSolution: {
      title: "The Age Old Problem",
      problem: "As we age, cellular degeneration becomes the leading cause of three kinds of diseases - Metabolic, Cardiovascular, and Neurodegenerative",
      solution: "Recent findings show a compelling link between increasing VO2 max by revitalizing mitochondria. Once the mitochondrial cells are revitalzied, the benefits cascade throughout the body, improving cardiovascular, metabolic, and cognitive health.",
      benefits: "Vitaliti Air is the worlld's first in-home device that helps regenerate mitochondria, leading to increased VO2 max and extended healthy lifespan.",
      labels: {
        problemLabel: "Problem",
        solutionLabel: "Solution",
        benefitsLabel: "Benefits"
      }
    },
    howItWorks: {
      title: "Transform Your Health in 3 Simple Steps",
      steps: [
        {
          number: 1,
          title: "Wear the Mask",
          image: "Product shot of the Vitaliti Air mask",
          text: "Put on your Vitaliti Air mask for just 30 minutes"
        },
        {
          number: 2,
          title: "Breathe Naturally",
          image: "Person relaxing while wearing mask, reading or working",
          text: "Continue your normal activities while the mask optimizes your oxygen intake"
        },
        {
          number: 3,
          title: "Track Your Progress",
          image: "App mockup showing VO2 max improvements",
          text: "Monitor your health improvements through our companion app"
        }
      ]
    },
    threePillars: {
      title: "One Device. Three Life-Changing Benefits.",
      layout: "Alternating left/right layouts with device mockups",
      pillars: [
        {
          title: "Cardiovascular Health",
          mockup: "Mockup showing heart rate and VO2 max metrics",
          description: "Strengthen your heart and improve oxygen delivery throughout your body"
        },
        {
          title: "Metabolic Health & Diabetes Prevention",
          mockup: "Mockup showing glucose levels and metabolic markers",
          description: "Optimize your metabolism and reduce diabetes risk"
        },
        {
          title: "Cognitive Performance",
          mockup: "Mockup showing focus and mental clarity metrics",
          description: "Enhance brain function and protect against cognitive decline"
        }
      ]
    },
    science: {
      title: "The Science",
      inactiveTitle: "The 4-Phase Science",
      inactiveDescription: "Vitaliti Air's precision altitude training follows a scientifically-proven 4-phase process that optimizes your cellular function. Click on any segment to explore the science behind each phase.",
      statistic: "Every 1 mL/kg/min increase in VO2 max = 9% reduction in mortality risk",
      citations: "Research citations displayed elegantly",
      socialCredibility: "Social credibility",
      visual: "Pictures of the three medical experts and links to studies (TBD)",
      segments: [
        {
          number: 1,
          title: "Hypoxic",
          description: "During the hypoxic phase, your body experiences carefully controlled low-oxygen conditions that mimic high-altitude environments. This oxygen restriction activates powerful cellular signaling pathways, triggering your cells to become more efficient and initiating the natural process of cellular renewal.",
          position: "top"
        },
        {
          number: 2,
          title: "Mitophagy",
          description: "Mitophagy is your body's natural quality control system. During this phase, damaged and dysfunctional mitochondria are identified and safely removed from your cells. This cellular housekeeping is essential for maintaining optimal energy production and preventing cellular damage that leads to fatigue and metabolic dysfunction.",
          position: "right"
        },
        {
          number: 3,
          title: "Hyperoxic",
          description: "The hyperoxic phase delivers oxygen-rich recovery periods that supercharge your cellular repair mechanisms. This controlled high-oxygen environment provides the optimal conditions for your cells to rebuild and strengthen, maximizing the benefits gained during the hypoxic phase.",
          position: "bottom"
        },
        {
          number: 4,
          title: "Mitochondrial Genesis",
          description: "The final phase stimulates mitochondrial genesis - the creation of brand new, highly efficient mitochondria. These fresh powerhouses replace the old ones removed during mitophagy, dramatically improving your cellular energy production capacity and leading to the measurable improvements in insulin sensitivity, reduced fatigue, and enhanced physical performance.",
          position: "left"
        }
      ]
    },
    productFeatures: {
      title: "Vitaliti Air",
      subtitle: "Explore the revolutionary altitude training device that delivers measurable health benefits",
      deviceAltText: "Vitaliti Air Device",
      hotspots: [
        {
          id: 1,
          x: 55,
          y: 25,
          title: "Power Button",
          description: "Simple one-touch operation",
          details: "The intuitive power button provides effortless device control. Simply press once to begin your precision altitude training session, and press again to safely power down. LED indicators confirm operational status and session progress."
        },
        {
          id: 2,
          x: 35,
          y: 20,
          title: "Digital Display",
          description: "Real-time FiO2 monitoring",
          details: "The high-resolution display provides precise, real-time feedback on your oxygen levels (FiO2). Monitor your training intensity and track your progress through each phase of the hypoxic and hyperoxic cycles with clinical-grade accuracy."
        },
        {
          id: 3,
          x: 25,
          y: 65,
          title: "Hypoxia Delivery",
          description: "Primary breathing interface",
          details: "The main breathing tube delivers precisely controlled low-oxygen air during hypoxic phases. Medical-grade tubing ensures safe, comfortable delivery of altitude-simulated conditions while maintaining optimal air flow and temperature."
        },
        {
          id: 4,
          x: 53,
          y: 75,
          title: "Hyperoxia Line",
          description: "Recovery phase enhancement",
          details: "The secondary delivery system provides oxygen-rich air during hyperoxic recovery phases. This sophisticated dual-delivery system optimizes cellular repair and maximizes the benefits of each training cycle."
        }
      ]
    },
    waitlistCTA: {
      background: "Full-width gradient section",
      headline: "Be Among the First to Experience Vitaliti Air",
      subheadline: "Limited early access available. Join our waitlist today.",
      emailPlaceholder: "Enter your email address",
      button: "JOIN WAITLIST",
      text: "Get exclusive early-bird pricing and be first to know when we launch",
      successTitle: "Thank you for joining!",
      successMessage: "You're now on the waitlist. We'll be in touch soon!"
    },
    team: {
      title: "Our Team",
      members: [
        {
          id: 1,
          name: "Brian Kennedy, PhD",
          title: "Director at Centre for Healthy Longevity at National University of Singapore",
          photo: "/images/brian-kennedy.jpeg"
        },
        {
          id: 2,
          name: "Nathan Price, PhD",
          title: "Professor and Co-Director, Center for Human Healthspan at Buck Institute for Research on Aging",
          photo: "/images/nathan-price.jpeg"
        },
        {
          id: 3,
          name: "Stanley Rockson, MD",
          title: "Chief of Consultative Cardiology at Stanford University",
          photo: "/images/stanley-rockson.jpg"
        }
      ]
    },
    videoHero: {
      videos: [
        {
          id: 1,
          title: "Metabolic Transformation",
          subtitle: "Revolutionary altitude training technology that enhances insulin sensitivity and glucose regulation without traditional exercise",
          videoUrl: "/videos/hero-video-1.mp4",
          isVideo: true,
          number: "01",
          statistic: "33%",
          statType: "improvement",
          statMetric: "Insulin sensitivity"
        },
        {
          id: 2,
          title: "Boundless Energy",
          subtitle: "Experience dramatic fatigue reduction and sustained vitality through precision altitude exposure protocols",
          videoUrl: "/videos/hero-video-2.mp4",
          isVideo: true,
          number: "02",
          statistic: "30%",
          statType: "reduction",
          statMetric: "Fatigue"
        },
        {
          id: 3,
          title: "Cardiovascular Excellence",
          subtitle: "Unlock your body's peak aerobic capacity and endurance potential with cutting-edge hypoxic conditioning",
          videoUrl: "/videos/hero-video-4.mp4",
          isVideo: true,
          number: "03",
          statistic: "20%",
          statType: "improvement",
          statMetric: "Vo2 max"
        },
        {
          id: 4,
          title: "Peak Performance",
          subtitle: "Achieve unprecedented physical capacity improvements through scientifically-proven altitude training methods",
          videoUrl: "/videos/hero-video-3.mp4",
          isVideo: true,
          number: "04",
          statistic: "24%",
          statType: "improvement",
          statMetric: "Physical capacity"
        }
      ]
    }
  }
}

// Get content for a specific section
export function getSectionContent(sectionId: number) {
  const content = parseHomeContent()

  switch (sectionId) {
    case 1: return content.hero
    case 2: return content.productOverview
    case 3: return content.problemSolution
    case 4: return content.howItWorks
    case 5: return content.threePillars
    case 6: return content.science
    case 7: return content.productFeatures
    case 8: return content.waitlistCTA
    case 9: return content.team
    case 10: return content.videoHero
    default: return null
  }
}
