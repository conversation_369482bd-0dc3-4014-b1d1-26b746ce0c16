// Animation utilities for Framer Motion
// Provides reusable animation variants and utilities for consistent behavior

import { Variants, Transition } from 'framer-motion'

// Check if user prefers reduced motion
export const prefersReducedMotion = () => {
  if (typeof window === 'undefined') return false
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches
}

// Base transition configurations
export const transitions = {
  smooth: { duration: 0.6, ease: "easeOut" } as Transition,
  quick: { duration: 0.3, ease: "easeOut" } as Transition,
  slow: { duration: 1.0, ease: "easeOut" } as Transition,
  spring: { type: "spring", stiffness: 100, damping: 15 } as Transition,
  bouncy: { type: "spring", stiffness: 200, damping: 10 } as Transition,
}

// Text animation variants
export const fadeInUp: Variants = {
  hidden: { 
    opacity: 0, 
    y: 30 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: transitions.smooth
  }
}

export const fadeInDown: Variants = {
  hidden: { 
    opacity: 0, 
    y: -30 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: transitions.smooth
  }
}

export const fadeInLeft: Variants = {
  hidden: { 
    opacity: 0, 
    x: -30 
  },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: transitions.smooth
  }
}

export const fadeInRight: Variants = {
  hidden: { 
    opacity: 0, 
    x: 30 
  },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: transitions.smooth
  }
}

export const fadeIn: Variants = {
  hidden: { 
    opacity: 0 
  },
  visible: { 
    opacity: 1,
    transition: transitions.smooth
  }
}

export const scaleIn: Variants = {
  hidden: { 
    opacity: 0, 
    scale: 0.8 
  },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: transitions.smooth
  }
}

export const slideInUp: Variants = {
  hidden: { 
    y: 100, 
    opacity: 0 
  },
  visible: { 
    y: 0, 
    opacity: 1,
    transition: transitions.smooth
  }
}

// Container variants for staggered animations
export const staggerContainer: Variants = {
  hidden: { 
    opacity: 0 
  },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
}

export const staggerContainerFast: Variants = {
  hidden: { 
    opacity: 0 
  },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.05
    }
  }
}

export const staggerContainerSlow: Variants = {
  hidden: { 
    opacity: 0 
  },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.3,
      delayChildren: 0.2
    }
  }
}

// Hero section specific variants
export const heroContainer: Variants = {
  hidden: { 
    opacity: 0 
  },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.3,
      delayChildren: 0.2
    }
  }
}

export const heroText: Variants = {
  hidden: { 
    opacity: 0, 
    y: 50 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { 
      duration: 0.8, 
      ease: "easeOut" 
    }
  }
}

export const heroButton: Variants = {
  hidden: { 
    opacity: 0, 
    scale: 0.8 
  },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: { 
      duration: 0.6, 
      ease: "easeOut",
      delay: 0.2
    }
  }
}

// Section animation variants
export const sectionContainer: Variants = {
  hidden: { 
    opacity: 0 
  },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.1
    }
  }
}

// Scroll-based animation variants
export const scrollFadeIn: Variants = {
  hidden: { 
    opacity: 0, 
    y: 50 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { 
      duration: 0.8, 
      ease: "easeOut" 
    }
  }
}

// Card animation variants
export const cardHover: Variants = {
  rest: { 
    scale: 1,
    transition: transitions.quick
  },
  hover: { 
    scale: 1.02,
    transition: transitions.quick
  }
}

// Utility function to get animation variants based on user preference
export const getAnimationVariants = (variants: Variants): Variants => {
  if (prefersReducedMotion()) {
    // Return simplified variants for reduced motion
    return {
      hidden: { opacity: 0 },
      visible: { opacity: 1, transition: { duration: 0.2 } }
    }
  }
  return variants
}

// Utility function to get transition based on user preference
export const getTransition = (transition: Transition): Transition => {
  if (prefersReducedMotion()) {
    return { duration: 0.2, ease: "easeOut" }
  }
  return transition
}

// Viewport configuration for intersection observer
export const defaultViewport = {
  once: true,
  margin: "-100px",
  amount: 0.3
}

export const immediateViewport = {
  once: true,
  margin: "0px",
  amount: 0.1
}

export const delayedViewport = {
  once: true,
  margin: "-200px",
  amount: 0.5
}
