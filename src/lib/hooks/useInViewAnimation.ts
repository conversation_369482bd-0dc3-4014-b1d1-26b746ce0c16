// Custom hook for intersection observer-based animations
import { useInView } from 'framer-motion'
import { useRef, RefObject } from 'react'
import { prefersReducedMotion } from '../animations'

interface UseInViewAnimationOptions {
  threshold?: number
  once?: boolean
}

interface InViewAnimationResult {
  ref: RefObject<HTMLDivElement | null>
  isInView: boolean
  controls: {
    initial: string
    animate: string
    transition: object
  }
}

export function useInViewAnimation(
  options: UseInViewAnimationOptions = {}
): InViewAnimationResult {
  const {
    threshold = 0.3,
    once = true
  } = options

  const ref = useRef<HTMLDivElement>(null)
  const isInView = useInView(ref, {
    amount: threshold,
    once
  })

  const shouldAnimate = !prefersReducedMotion()

  const controls = {
    initial: "hidden",
    animate: isInView ? "visible" : "hidden",
    transition: shouldAnimate 
      ? { duration: 0.6, ease: "easeOut" }
      : { duration: 0.2, ease: "easeOut" }
  }

  return { ref, isInView, controls }
}

interface UseStaggeredInViewOptions extends UseInViewAnimationOptions {
  staggerDelay?: number
}

export function useStaggeredInView(
  options: UseStaggeredInViewOptions = {}
): InViewAnimationResult {
  const {
    threshold = 0.2,
    once = true,
    staggerDelay = 0.1
  } = options

  const ref = useRef<HTMLDivElement>(null)
  const isInView = useInView(ref, {
    amount: threshold,
    once
  })

  const shouldAnimate = !prefersReducedMotion()

  const controls = {
    initial: "hidden",
    animate: isInView ? "visible" : "hidden",
    transition: shouldAnimate 
      ? { 
          staggerChildren: staggerDelay,
          delayChildren: 0.1
        }
      : { 
          staggerChildren: 0.05,
          delayChildren: 0.05
        }
  }

  return { ref, isInView, controls }
}

export function useScrollTriggeredAnimation(
  threshold: number = 0.3
) {
  const ref = useRef<HTMLDivElement>(null)
  const isInView = useInView(ref, {
    amount: threshold
  })

  return { ref, isInView }
}

interface UseDelayedInViewOptions extends UseInViewAnimationOptions {
  delay?: number
}

export function useDelayedInView(
  options: UseDelayedInViewOptions = {}
): InViewAnimationResult {
  const {
    threshold = 0.3,
    once = true,
    delay = 0.2
  } = options

  const ref = useRef<HTMLDivElement>(null)
  const isInView = useInView(ref, {
    amount: threshold,
    once
  })

  const shouldAnimate = !prefersReducedMotion()

  const controls = {
    initial: "hidden",
    animate: isInView ? "visible" : "hidden",
    transition: shouldAnimate 
      ? { 
          duration: 0.6, 
          ease: "easeOut",
          delay: isInView ? delay : 0
        }
      : { 
          duration: 0.2, 
          ease: "easeOut",
          delay: 0
        }
  }

  return { ref, isInView, controls }
}

// Hook for animating elements when they come into view with custom variants
export function useCustomInView(
  variants: object,
  options: UseInViewAnimationOptions = {}
) {
  const {
    threshold = 0.3,
    once = true
  } = options

  const ref = useRef<HTMLDivElement>(null)
  const isInView = useInView(ref, {
    amount: threshold,
    once
  })

  return {
    ref,
    isInView,
    variants: prefersReducedMotion()
      ? { hidden: { opacity: 0 }, visible: { opacity: 1 } }
      : variants,
    initial: "hidden",
    animate: isInView ? "visible" : "hidden"
  }
}
