// Presentation-style section transition hook
import { useScroll, useTransform, useMotionValue, useSpring } from 'framer-motion'
import { useEffect, useState, useCallback, useRef } from 'react'

interface UsePresentationTransitionOptions {
  threshold?: number
  transitionDuration?: number
  springConfig?: {
    stiffness: number
    damping: number
  }
}

interface PresentationTransitionState {
  currentSection: number
  isTransitioning: boolean
  progress: number
}

export function usePresentationTransition(
  options: UsePresentationTransitionOptions = {}
) {
  const {
    threshold = 50,
    transitionDuration = 1000,
    springConfig = { stiffness: 100, damping: 30 }
  } = options

  const [state, setState] = useState<PresentationTransitionState>({
    currentSection: 0,
    isTransitioning: false,
    progress: 0
  })

  const { scrollY } = useScroll()
  const transitionProgress = useMotionValue(0)
  const springProgress = useSpring(transitionProgress, springConfig)

  // Track if user has initiated scroll from hero section
  const [hasInitiatedTransition, setHasInitiatedTransition] = useState(false)
  const [isTransitionComplete, setIsTransitionComplete] = useState(false)

  // Refs for cleanup and fallback
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const originalBodyStyles = useRef<{
    overflow: string
    position: string
    top: string
    width: string
  } | null>(null)

  // Get viewport height safely
  const [viewportHeight, setViewportHeight] = useState(800)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setViewportHeight(window.innerHeight)

      const handleResize = () => setViewportHeight(window.innerHeight)
      window.addEventListener('resize', handleResize)
      return () => window.removeEventListener('resize', handleResize)
    }
  }, [])

  // Hero section transforms
  const heroY = useTransform(springProgress, [0, 1], [0, -viewportHeight])
  const heroOpacity = useTransform(springProgress, [0, 0.3], [1, 0])
  const heroScale = useTransform(springProgress, [0, 1], [1, 0.8])

  // Section 2 transforms
  const section2Y = useTransform(springProgress, [0, 1], [viewportHeight, 0])
  const section2Opacity = useTransform(springProgress, [0.7, 1], [0, 1])

  // Utility functions for body style management
  const saveOriginalBodyStyles = useCallback(() => {
    if (typeof document !== 'undefined') {
      originalBodyStyles.current = {
        overflow: document.body.style.overflow || 'auto',
        position: document.body.style.position || 'static',
        top: document.body.style.top || 'auto',
        width: document.body.style.width || 'auto'
      }
    }
  }, [])

  const restoreBodyStyles = useCallback(() => {
    if (typeof document !== 'undefined' && originalBodyStyles.current) {
      const { overflow, position, top, width } = originalBodyStyles.current
      document.body.style.overflow = overflow
      document.body.style.position = position
      document.body.style.top = top
      document.body.style.width = width
      originalBodyStyles.current = null
    }
  }, [])

  const forceRestoreScrolling = useCallback(() => {
    if (typeof document !== 'undefined') {
      document.body.style.overflow = 'auto'
      document.body.style.position = 'static'
      document.body.style.top = 'auto'
      document.body.style.width = 'auto'
    }
  }, [])

  // Handle scroll detection and transition trigger
  useEffect(() => {
    const unsubscribe = scrollY.onChange((latest) => {
      // Only trigger if we're in the hero section and haven't started transition
      if (latest > threshold && !hasInitiatedTransition && !isTransitionComplete) {
        // Save original styles before modifying
        saveOriginalBodyStyles()

        // Clear any existing timeout
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current)
        }

        // Temporarily disable scrolling with a lighter approach
        document.body.style.overflow = 'hidden'

        setHasInitiatedTransition(true)
        setState(prev => ({ ...prev, isTransitioning: true }))

        // Hide scroll indicator
        const scrollIndicator = document.querySelector('[data-scroll-indicator]')
        if (scrollIndicator) {
          (scrollIndicator as HTMLElement).style.opacity = '0'
        }

        // Start the presentation transition
        transitionProgress.set(1)

        // Complete transition after animation with multiple fallbacks
        timeoutRef.current = setTimeout(() => {
          setState(prev => ({
            ...prev,
            currentSection: 1,
            isTransitioning: false,
            progress: 1
          }))
          setIsTransitionComplete(true)

          // Restore scrolling
          restoreBodyStyles()

          // Smoothly position at section 2
          setTimeout(() => {
            const section2Element = document.getElementById('product-overview')
            if (section2Element) {
              section2Element.scrollIntoView({ behavior: 'smooth', block: 'start' })
            }
          }, 100)

        }, transitionDuration)

        // Emergency fallback to restore scrolling
        setTimeout(() => {
          if (state.isTransitioning) {
            console.warn('Emergency scroll restoration triggered')
            forceRestoreScrolling()
            setIsTransitionComplete(true)
            setState(prev => ({ ...prev, isTransitioning: false }))
          }
        }, transitionDuration + 1000)
      }
    })

    return () => {
      unsubscribe()
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [scrollY, threshold, hasInitiatedTransition, isTransitionComplete, transitionDuration, transitionProgress, saveOriginalBodyStyles, restoreBodyStyles, forceRestoreScrolling, state.isTransitioning])

  // Cleanup effect to ensure scroll is never permanently locked
  useEffect(() => {
    const handleBeforeUnload = () => {
      forceRestoreScrolling()
    }

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && state.isTransitioning) {
        // If page becomes visible and we're still transitioning, force restore
        setTimeout(() => {
          if (state.isTransitioning) {
            forceRestoreScrolling()
            setIsTransitionComplete(true)
            setState(prev => ({ ...prev, isTransitioning: false }))
          }
        }, 500)
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      // Always restore scrolling on unmount
      forceRestoreScrolling()
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [forceRestoreScrolling, state.isTransitioning])

  // Reset function for development/testing
  const resetTransition = useCallback(() => {
    // Clear any pending timeouts
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    setHasInitiatedTransition(false)
    setIsTransitionComplete(false)
    transitionProgress.set(0)
    setState({
      currentSection: 0,
      isTransitioning: false,
      progress: 0
    })

    // Force restore scrolling
    forceRestoreScrolling()

    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }, [transitionProgress, forceRestoreScrolling])

  // Handle manual section navigation (for future use)
  const goToSection = useCallback((sectionIndex: number) => {
    if (sectionIndex === 0) {
      resetTransition()
    } else if (sectionIndex === 1 && !isTransitionComplete) {
      setHasInitiatedTransition(true)
      setState(prev => ({ ...prev, isTransitioning: true }))
      transitionProgress.set(1)
    }
  }, [resetTransition, isTransitionComplete, transitionProgress])

  return {
    // State
    currentSection: state.currentSection,
    isTransitioning: state.isTransitioning,
    progress: springProgress,
    
    // Transform values for hero section
    heroTransforms: {
      y: heroY,
      opacity: heroOpacity,
      scale: heroScale
    },
    
    // Transform values for section 2
    section2Transforms: {
      y: section2Y,
      opacity: section2Opacity
    },
    
    // Control functions
    goToSection,
    resetTransition,
    
    // Status flags
    hasInitiatedTransition,
    isTransitionComplete
  }
}
