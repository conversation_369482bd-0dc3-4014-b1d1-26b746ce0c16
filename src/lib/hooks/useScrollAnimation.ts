// Custom hook for scroll-based animations
import { useScroll, useTransform, MotionValue } from 'framer-motion'
import { useEffect, useState, RefObject } from 'react'

interface UseScrollAnimationOptions {
  target?: RefObject<HTMLElement>
}

interface ScrollAnimationValues {
  scrollY: MotionValue<number>
  scrollYProgress: MotionValue<number>
  opacity: MotionValue<number>
  scale: MotionValue<number>
  y: MotionValue<number>
}

export function useScrollAnimation(
  options: UseScrollAnimationOptions = {}
): ScrollAnimationValues {
  const { target } = options

  const { scrollY, scrollYProgress } = useScroll({
    target
  })

  // Create transform values for common animations
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0])
  const scale = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.8, 1, 1, 0.8])
  const y = useTransform(scrollYProgress, [0, 1], [100, -100])

  return {
    scrollY,
    scrollYProgress,
    opacity,
    scale,
    y
  }
}

interface UseAutoScrollOptions {
  threshold?: number
  targetId?: string
  delay?: number
}

export function useAutoScroll(options: UseAutoScrollOptions = {}) {
  const { threshold = 100, targetId = 'product-overview', delay = 0 } = options
  const [hasAutoScrolled, setHasAutoScrolled] = useState(false)
  const { scrollY } = useScroll()

  useEffect(() => {
    const unsubscribe = scrollY.onChange((latest) => {
      if (latest > threshold && !hasAutoScrolled) {
        setTimeout(() => {
          const targetElement = document.getElementById(targetId)
          if (targetElement) {
            targetElement.scrollIntoView({ 
              behavior: 'smooth',
              block: 'start'
            })
            setHasAutoScrolled(true)
          }
        }, delay)
      }
    })
    
    return unsubscribe
  }, [scrollY, hasAutoScrolled, threshold, targetId, delay])

  return { hasAutoScrolled, setHasAutoScrolled }
}

interface UseScrollProgressOptions {
  target?: RefObject<HTMLElement>
}

export function useScrollProgress(options: UseScrollProgressOptions = {}) {
  const { target } = options
  const { scrollYProgress } = useScroll({ target })
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    const unsubscribe = scrollYProgress.onChange((latest) => {
      setProgress(Math.round(latest * 100))
    })
    
    return unsubscribe
  }, [scrollYProgress])

  return progress
}

interface UseParallaxOptions {
  speed?: number
  direction?: 'up' | 'down'
}

export function useParallax(options: UseParallaxOptions = {}) {
  const { speed = 0.5, direction = 'up' } = options
  const { scrollY } = useScroll()
  
  const y = useTransform(
    scrollY,
    [0, 1000],
    direction === 'up' ? [0, -1000 * speed] : [0, 1000 * speed]
  )

  return y
}

interface UseScrollDirectionOptions {
  threshold?: number
}

export function useScrollDirection(options: UseScrollDirectionOptions = {}) {
  const { threshold = 10 } = options
  const [scrollDirection, setScrollDirection] = useState<'up' | 'down' | null>(null)
  const [lastScrollY, setLastScrollY] = useState(0)

  useEffect(() => {
    const updateScrollDirection = () => {
      const scrollY = window.scrollY
      const direction = scrollY > lastScrollY ? 'down' : 'up'
      
      if (Math.abs(scrollY - lastScrollY) >= threshold) {
        setScrollDirection(direction)
        setLastScrollY(scrollY)
      }
    }

    window.addEventListener('scroll', updateScrollDirection)
    return () => window.removeEventListener('scroll', updateScrollDirection)
  }, [lastScrollY, threshold])

  return scrollDirection
}
