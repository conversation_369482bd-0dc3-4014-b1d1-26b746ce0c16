# AGENT.md - Vitaliti Air Website Development Guide

## Commands
- **Build**: `pnpm run build` - Build for production
- **Dev**: `pnpm run dev` - Start development server with Turbopack
- **Lint**: `pnpm run lint` - Run ESLint (Next.js config)
- **Type Check**: Run build to check TypeScript
- **Clean**: `pnpm run clean` - Remove node_modules, .next, pnpm-lock.yaml

## Architecture
- **Framework**: Next.js 15.3.4 App Router
- **Package Manager**: pnpm (migration complete, use pnpm not npm)
- **Structure**: `/src/app/` (pages), `/src/components/` (UI), `/src/lib/` (utilities)
- **Theme**: Centralized CSS variables in `globals.css` - see THEME_SYSTEM_DOCUMENTATION.md
- **Legacy**: Original design preserved in `/legacy/` folder and `legacy-design` branch

## Code Style
- **Imports**: Use `@/` alias for src directory (configured in tsconfig.json)
- **Components**: Export as named functions, use TypeScript interfaces for props
- **Styling**: Tailwind CSS with CSS variables, shadcn/ui components in `/components/ui/`
- **Animations**: Framer Motion with custom hooks in `/lib/hooks/`
- **Content**: Centralized content management in `/lib/content.ts`
- **Icons**: Lucide React for icons
- **Image**: Use Next.js Image component

## Conventions
- Use `cn()` utility from `/lib/utils.ts` for conditional classes
- Follow shadcn/ui patterns for UI components
- CSS variables defined in globals.css for theming
- TypeScript strict mode enabled
- ESLint with Next.js core-web-vitals config
